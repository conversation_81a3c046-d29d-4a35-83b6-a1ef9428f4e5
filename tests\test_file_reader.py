"""
测试文件读取模块
"""

import pytest
import pandas as pd
from pathlib import Path
import tempfile
import os

from mcp_semantic_splitter.file_reader import TableFileReader


class TestTableFileReader:
    """测试表格文件读取器"""
    
    def setup_method(self):
        """设置测试环境"""
        self.reader = TableFileReader()
    
    def test_is_supported(self):
        """测试文件格式支持检查"""
        # 支持的格式
        assert self.reader.is_supported("test.xlsx")
        assert self.reader.is_supported("test.csv")
        assert self.reader.is_supported("test.xls")
        
        # 不支持的格式
        assert not self.reader.is_supported("test.txt")
        assert not self.reader.is_supported("test.pdf")
        assert not self.reader.is_supported("test.doc")
    
    def test_read_csv_file(self):
        """测试读取CSV文件"""
        # 创建临时CSV文件
        csv_data = """名称,年龄,城市
张三,25,北京
李四,30,上海
王五,28,广州"""
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False, encoding='utf-8') as f:
            f.write(csv_data)
            temp_file = f.name
        
        try:
            chunks = self.reader.read_file(temp_file)
            
            # 验证结果
            assert len(chunks) > 0
            assert any("张三" in chunk for chunk in chunks)
            assert any("李四" in chunk for chunk in chunks)
            assert any("王五" in chunk for chunk in chunks)
            
        finally:
            os.unlink(temp_file)
    
    def test_read_nonexistent_file(self):
        """测试读取不存在的文件"""
        with pytest.raises(FileNotFoundError):
            self.reader.read_file("nonexistent.csv")
    
    def test_read_unsupported_format(self):
        """测试读取不支持的文件格式"""
        with tempfile.NamedTemporaryFile(suffix='.txt', delete=False) as f:
            temp_file = f.name
        
        try:
            with pytest.raises(ValueError, match="不支持的文件格式"):
                self.reader.read_file(temp_file)
        finally:
            os.unlink(temp_file)
    
    def test_dataframe_to_text_chunks(self):
        """测试DataFrame转文本片段"""
        # 创建测试DataFrame
        df = pd.DataFrame({
            '产品': ['苹果', '香蕉', '橙子'],
            '价格': [5.99, 3.99, 4.99],
            '类别': ['水果', '水果', '水果']
        })
        
        chunks = self.reader._dataframe_to_text_chunks(df)
        
        # 验证结果
        assert len(chunks) > 0
        # 应该包含表头
        assert any("表头" in chunk for chunk in chunks)
        # 应该包含数据行
        assert any("苹果" in chunk for chunk in chunks)
        assert any("香蕉" in chunk for chunk in chunks)
    
    def test_empty_dataframe(self):
        """测试空DataFrame"""
        df = pd.DataFrame()
        chunks = self.reader._dataframe_to_text_chunks(df)
        assert chunks == []
    
    def test_get_sheet_names_csv(self):
        """测试获取CSV文件的工作表名称"""
        with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False, encoding='utf-8') as f:
            f.write("col1,col2\n1,2\n")
            temp_file = f.name
        
        try:
            sheet_names = self.reader.get_sheet_names(temp_file)
            assert sheet_names == ["Sheet1"]
        finally:
            os.unlink(temp_file)
