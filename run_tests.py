#!/usr/bin/env python3
"""
运行测试脚本
"""

import subprocess
import sys
from pathlib import Path


def run_tests():
    """运行所有测试"""
    print("=== 运行MCP语义分割器测试 ===\n")
    
    # 检查pytest是否安装
    try:
        import pytest
        print("✓ pytest已安装")
    except ImportError:
        print("✗ pytest未安装，正在安装...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", "pytest", "pytest-asyncio"])
        print("✓ pytest安装完成")
    
    # 运行测试
    test_dir = Path(__file__).parent / "tests"
    
    if not test_dir.exists():
        print(f"✗ 测试目录不存在: {test_dir}")
        return False
    
    print(f"\n运行测试目录: {test_dir}")
    
    try:
        # 运行pytest
        result = subprocess.run([
            sys.executable, "-m", "pytest", 
            str(test_dir),
            "-v",  # 详细输出
            "--tb=short"  # 简短的错误回溯
        ], capture_output=True, text=True)
        
        print("测试输出:")
        print(result.stdout)
        
        if result.stderr:
            print("错误输出:")
            print(result.stderr)
        
        if result.returncode == 0:
            print("\n✓ 所有测试通过!")
            return True
        else:
            print(f"\n✗ 测试失败，退出码: {result.returncode}")
            return False
            
    except Exception as e:
        print(f"✗ 运行测试时出错: {str(e)}")
        return False


def run_example():
    """运行使用示例"""
    print("\n=== 运行使用示例 ===\n")
    
    example_file = Path(__file__).parent / "examples" / "usage_example.py"
    
    if not example_file.exists():
        print(f"✗ 示例文件不存在: {example_file}")
        return False
    
    try:
        result = subprocess.run([
            sys.executable, str(example_file)
        ], capture_output=True, text=True)
        
        print("示例输出:")
        print(result.stdout)
        
        if result.stderr:
            print("错误输出:")
            print(result.stderr)
        
        if result.returncode == 0:
            print("\n✓ 示例运行成功!")
            return True
        else:
            print(f"\n✗ 示例运行失败，退出码: {result.returncode}")
            return False
            
    except Exception as e:
        print(f"✗ 运行示例时出错: {str(e)}")
        return False


def main():
    """主函数"""
    print("MCP语义分割器 - 测试和示例运行器\n")
    
    # 检查Python版本
    if sys.version_info < (3, 8):
        print(f"✗ 需要Python 3.8或更高版本，当前版本: {sys.version}")
        return
    
    print(f"✓ Python版本: {sys.version}")
    
    # 运行测试
    test_success = run_tests()
    
    # 运行示例（如果测试通过）
    if test_success:
        example_success = run_example()
    else:
        print("\n跳过示例运行（测试未通过）")
        example_success = False
    
    # 总结
    print("\n" + "="*50)
    print("总结:")
    print(f"  测试: {'✓ 通过' if test_success else '✗ 失败'}")
    print(f"  示例: {'✓ 成功' if example_success else '✗ 失败'}")
    
    if test_success and example_success:
        print("\n🎉 所有检查都通过了！MCP语义分割器已准备就绪。")
    else:
        print("\n⚠️  存在问题，请检查上面的错误信息。")


if __name__ == "__main__":
    main()
