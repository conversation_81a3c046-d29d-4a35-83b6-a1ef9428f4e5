"""
表格文件读取模块
支持多种表格格式的文件读取，将表格数据转换为文本片段
"""

import os
import pandas as pd
from typing import List, Dict, Any, Optional
from pathlib import Path
import logging

logger = logging.getLogger(__name__)


class TableFileReader:
    """表格文件读取器"""
    
    SUPPORTED_EXTENSIONS = {
        '.xlsx', '.xls', '.xlsb', '.xlsm',  # Excel格式
        '.csv',  # CSV格式
        '.et', '.ett', '.ets',  # WPS表格格式
        '.xlt', '.xltx', '.xltm'  # Excel模板格式
    }
    
    def __init__(self):
        """初始化文件读取器"""
        pass
    
    def is_supported(self, file_path: str) -> bool:
        """检查文件格式是否支持"""
        ext = Path(file_path).suffix.lower()
        return ext in self.SUPPORTED_EXTENSIONS
    
    def read_file(self, file_path: str, sheet_name: Optional[str] = None) -> List[str]:
        """
        读取表格文件并转换为文本片段
        
        Args:
            file_path: 文件路径
            sheet_name: 工作表名称（仅对Excel文件有效）
            
        Returns:
            文本片段列表
        """
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"文件不存在: {file_path}")
        
        if not self.is_supported(file_path):
            raise ValueError(f"不支持的文件格式: {Path(file_path).suffix}")
        
        try:
            # 根据文件扩展名选择读取方法
            ext = Path(file_path).suffix.lower()
            
            if ext == '.csv':
                df = self._read_csv(file_path)
            elif ext in {'.xlsx', '.xls', '.xlsb', '.xlsm', '.xlt', '.xltx', '.xltm'}:
                df = self._read_excel(file_path, sheet_name)
            elif ext in {'.et', '.ett', '.ets'}:
                # WPS格式尝试用Excel方法读取
                df = self._read_excel(file_path, sheet_name)
            else:
                raise ValueError(f"未实现的文件格式处理: {ext}")
            
            # 转换为文本片段
            text_chunks = self._dataframe_to_text_chunks(df)
            
            logger.info(f"成功读取文件 {file_path}，生成 {len(text_chunks)} 个文本片段")
            return text_chunks
            
        except Exception as e:
            logger.error(f"读取文件失败 {file_path}: {str(e)}")
            raise
    
    def _read_csv(self, file_path: str) -> pd.DataFrame:
        """读取CSV文件"""
        # 尝试不同的编码
        encodings = ['utf-8', 'gbk', 'gb2312', 'utf-8-sig']
        
        for encoding in encodings:
            try:
                df = pd.read_csv(file_path, encoding=encoding)
                logger.debug(f"使用编码 {encoding} 成功读取CSV文件")
                return df
            except UnicodeDecodeError:
                continue
        
        # 如果所有编码都失败，使用默认编码并忽略错误
        df = pd.read_csv(file_path, encoding='utf-8', errors='ignore')
        logger.warning("使用UTF-8编码并忽略错误读取CSV文件")
        return df
    
    def _read_excel(self, file_path: str, sheet_name: Optional[str] = None) -> pd.DataFrame:
        """读取Excel文件"""
        try:
            # 如果指定了工作表名称
            if sheet_name:
                df = pd.read_excel(file_path, sheet_name=sheet_name, engine='openpyxl')
            else:
                # 读取第一个工作表
                df = pd.read_excel(file_path, engine='openpyxl')
            
            return df
        except Exception as e:
            # 尝试使用xlrd引擎（适用于老版本Excel文件）
            try:
                if sheet_name:
                    df = pd.read_excel(file_path, sheet_name=sheet_name, engine='xlrd')
                else:
                    df = pd.read_excel(file_path, engine='xlrd')
                return df
            except:
                raise e
    
    def _dataframe_to_text_chunks(self, df: pd.DataFrame) -> List[str]:
        """将DataFrame转换为文本片段"""
        text_chunks = []
        
        # 处理列名
        if not df.columns.empty:
            header_text = " | ".join([str(col) for col in df.columns if pd.notna(col)])
            if header_text.strip():
                text_chunks.append(f"表头: {header_text}")
        
        # 处理每一行数据
        for idx, row in df.iterrows():
            # 过滤掉空值和NaN
            row_values = [str(val) for val in row if pd.notna(val) and str(val).strip()]
            
            if row_values:
                row_text = " | ".join(row_values)
                text_chunks.append(f"第{idx+1}行: {row_text}")
        
        # 过滤掉过短的片段
        filtered_chunks = [chunk for chunk in text_chunks if len(chunk.strip()) > 5]
        
        return filtered_chunks
    
    def get_sheet_names(self, file_path: str) -> List[str]:
        """获取Excel文件的所有工作表名称"""
        if not self.is_supported(file_path):
            raise ValueError(f"不支持的文件格式: {Path(file_path).suffix}")
        
        ext = Path(file_path).suffix.lower()
        
        if ext == '.csv':
            return ["Sheet1"]  # CSV文件只有一个工作表
        
        try:
            # 使用openpyxl引擎
            excel_file = pd.ExcelFile(file_path, engine='openpyxl')
            return excel_file.sheet_names
        except:
            try:
                # 使用xlrd引擎
                excel_file = pd.ExcelFile(file_path, engine='xlrd')
                return excel_file.sheet_names
            except Exception as e:
                logger.error(f"无法获取工作表名称: {str(e)}")
                return []
