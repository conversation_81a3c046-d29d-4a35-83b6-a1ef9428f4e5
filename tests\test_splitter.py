"""
测试语义分割器
"""

import pytest
import numpy as np
from unittest.mock import Mock, patch

from mcp_semantic_splitter.splitter import SemanticSplitter


class TestSemanticSplitter:
    """测试语义分割器"""
    
    def setup_method(self):
        """设置测试环境"""
        # 使用mock避免加载真实模型
        with patch('mcp_semantic_splitter.splitter.EmbeddingModel') as mock_embedding:
            mock_embedding.return_value.encode.return_value = np.random.rand(5, 384)
            self.splitter = SemanticSplitter(
                similarity_threshold=0.7,
                min_chunk_size=10,
                max_chunk_size=100
            )
    
    def test_initialization(self):
        """测试初始化"""
        assert self.splitter.config["similarity_threshold"] == 0.7
        assert self.splitter.config["min_chunk_size"] == 10
        assert self.splitter.config["max_chunk_size"] == 100
    
    def test_split_text_chunks(self):
        """测试分割文本片段"""
        chunks = [
            "苹果是一种水果",
            "香蕉也是水果",
            "汽车是交通工具",
            "飞机也是交通工具",
            "电脑是电子设备"
        ]
        
        # Mock嵌入模型
        with patch.object(self.splitter.embedding_model, 'encode') as mock_encode:
            # 创建模拟的嵌入向量，使得相似的文本有更高的相似度
            embeddings = np.array([
                [1.0, 0.0, 0.0],  # 苹果
                [0.9, 0.1, 0.0],  # 香蕉 (与苹果相似)
                [0.0, 1.0, 0.0],  # 汽车
                [0.0, 0.9, 0.1],  # 飞机 (与汽车相似)
                [0.0, 0.0, 1.0]   # 电脑
            ])
            mock_encode.return_value = embeddings
            
            result = self.splitter.split_text_chunks(chunks)
            
            # 验证结果
            assert result["success"] is True
            assert "chunks" in result
            assert "statistics" in result
            assert result["original_chunks_count"] == 5
    
    def test_split_empty_chunks(self):
        """测试分割空片段列表"""
        result = self.splitter.split_text_chunks([])
        
        assert result["success"] is True
        assert result["chunks"] == []
        assert result["original_chunks_count"] == 0
        assert result["final_chunks_count"] == 0
    
    def test_update_config(self):
        """测试更新配置"""
        new_threshold = 0.8
        self.splitter.update_config(similarity_threshold=new_threshold)
        
        assert self.splitter.config["similarity_threshold"] == new_threshold
        assert self.splitter.similarity_calculator.get_threshold() == new_threshold
    
    def test_get_config(self):
        """测试获取配置"""
        config = self.splitter.get_config()
        
        assert isinstance(config, dict)
        assert "similarity_threshold" in config
        assert "min_chunk_size" in config
        assert "max_chunk_size" in config
    
    def test_get_supported_formats(self):
        """测试获取支持的格式"""
        formats = self.splitter.get_supported_formats()
        
        assert isinstance(formats, list)
        assert ".csv" in formats
        assert ".xlsx" in formats
    
    @patch('mcp_semantic_splitter.splitter.TableFileReader')
    def test_split_file(self, mock_reader_class):
        """测试分割文件"""
        # Mock文件读取器
        mock_reader = Mock()
        mock_reader.read_file.return_value = ["文本1", "文本2", "文本3"]
        mock_reader_class.return_value = mock_reader
        
        # Mock嵌入模型
        with patch.object(self.splitter.embedding_model, 'encode') as mock_encode:
            mock_encode.return_value = np.random.rand(3, 384)
            
            result = self.splitter.split_file("test.csv")
            
            # 验证结果
            assert result["success"] is True
            assert "file_path" in result
            assert result["file_path"] == "test.csv"
    
    def test_split_file_error(self):
        """测试文件分割错误处理"""
        # Mock文件读取器抛出异常
        with patch.object(self.splitter.file_reader, 'read_file') as mock_read:
            mock_read.side_effect = FileNotFoundError("文件不存在")
            
            result = self.splitter.split_file("nonexistent.csv")
            
            assert result["success"] is False
            assert "error" in result
