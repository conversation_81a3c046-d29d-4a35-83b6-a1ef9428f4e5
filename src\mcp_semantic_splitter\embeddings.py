"""
语义嵌入模块
使用预训练的向量嵌入模型将文本片段转换为语义向量
"""

import numpy as np
from typing import List, Optional, Union
from sentence_transformers import SentenceTransformer
import logging
import torch

logger = logging.getLogger(__name__)


class EmbeddingModel:
    """语义嵌入模型"""
    
    # 推荐的多语言模型
    DEFAULT_MODELS = {
        "multilingual": "sentence-transformers/paraphrase-multilingual-MiniLM-L12-v2",
        "chinese": "sentence-transformers/paraphrase-multilingual-mpnet-base-v2",
        "fast": "sentence-transformers/all-MiniLM-L6-v2",
        "accurate": "sentence-transformers/all-mpnet-base-v2"
    }
    
    def __init__(self, model_name: Optional[str] = None, device: Optional[str] = None):
        """
        初始化嵌入模型
        
        Args:
            model_name: 模型名称，如果为None则使用默认的多语言模型
            device: 设备类型 ('cpu', 'cuda', 'auto')
        """
        self.model_name = model_name or self.DEFAULT_MODELS["multilingual"]
        self.device = self._get_device(device)
        self.model = None
        self._load_model()
    
    def _get_device(self, device: Optional[str]) -> str:
        """获取计算设备"""
        if device == "auto" or device is None:
            return "cuda" if torch.cuda.is_available() else "cpu"
        return device
    
    def _load_model(self):
        """加载预训练模型"""
        try:
            logger.info(f"正在加载嵌入模型: {self.model_name}")
            self.model = SentenceTransformer(self.model_name, device=self.device)
            logger.info(f"模型加载成功，使用设备: {self.device}")
        except Exception as e:
            logger.error(f"模型加载失败: {str(e)}")
            # 尝试加载备用模型
            try:
                backup_model = self.DEFAULT_MODELS["fast"]
                logger.warning(f"尝试加载备用模型: {backup_model}")
                self.model = SentenceTransformer(backup_model, device=self.device)
                self.model_name = backup_model
                logger.info("备用模型加载成功")
            except Exception as backup_e:
                logger.error(f"备用模型加载也失败: {str(backup_e)}")
                raise RuntimeError("无法加载任何嵌入模型")
    
    def encode(self, texts: Union[str, List[str]], 
               batch_size: int = 32,
               show_progress_bar: bool = False,
               normalize_embeddings: bool = True) -> np.ndarray:
        """
        将文本编码为向量
        
        Args:
            texts: 单个文本或文本列表
            batch_size: 批处理大小
            show_progress_bar: 是否显示进度条
            normalize_embeddings: 是否标准化嵌入向量
            
        Returns:
            嵌入向量数组，形状为 (n_texts, embedding_dim)
        """
        if self.model is None:
            raise RuntimeError("模型未加载")
        
        if isinstance(texts, str):
            texts = [texts]
        
        if not texts:
            return np.array([])
        
        try:
            # 预处理文本
            processed_texts = self._preprocess_texts(texts)
            
            # 编码
            embeddings = self.model.encode(
                processed_texts,
                batch_size=batch_size,
                show_progress_bar=show_progress_bar,
                normalize_embeddings=normalize_embeddings,
                convert_to_numpy=True
            )
            
            logger.debug(f"成功编码 {len(texts)} 个文本，嵌入维度: {embeddings.shape[1]}")
            return embeddings
            
        except Exception as e:
            logger.error(f"文本编码失败: {str(e)}")
            raise
    
    def _preprocess_texts(self, texts: List[str]) -> List[str]:
        """预处理文本"""
        processed = []
        
        for text in texts:
            # 清理文本
            cleaned = text.strip()
            
            # 限制文本长度（避免过长文本影响性能）
            if len(cleaned) > 512:
                cleaned = cleaned[:512] + "..."
            
            # 如果文本为空，使用占位符
            if not cleaned:
                cleaned = "[空文本]"
            
            processed.append(cleaned)
        
        return processed
    
    def get_embedding_dimension(self) -> int:
        """获取嵌入向量的维度"""
        if self.model is None:
            raise RuntimeError("模型未加载")
        
        return self.model.get_sentence_embedding_dimension()
    
    def encode_single(self, text: str) -> np.ndarray:
        """编码单个文本"""
        embeddings = self.encode([text])
        return embeddings[0] if len(embeddings) > 0 else np.array([])
    
    def similarity(self, text1: str, text2: str) -> float:
        """计算两个文本的相似度"""
        embeddings = self.encode([text1, text2])
        if len(embeddings) != 2:
            return 0.0
        
        # 计算余弦相似度
        from sklearn.metrics.pairwise import cosine_similarity
        similarity_score = cosine_similarity([embeddings[0]], [embeddings[1]])[0][0]
        return float(similarity_score)
    
    def get_model_info(self) -> dict:
        """获取模型信息"""
        return {
            "model_name": self.model_name,
            "device": self.device,
            "embedding_dimension": self.get_embedding_dimension() if self.model else None,
            "max_seq_length": getattr(self.model, 'max_seq_length', None) if self.model else None
        }
    
    @classmethod
    def list_available_models(cls) -> dict:
        """列出可用的预设模型"""
        return cls.DEFAULT_MODELS.copy()
    
    def __del__(self):
        """清理资源"""
        if hasattr(self, 'model') and self.model is not None:
            del self.model
            if torch.cuda.is_available():
                torch.cuda.empty_cache()
