[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "mcp-semantic-splitter"
version = "0.1.0"
description = "MCP服务器：基于语义相似度的表格文档智能分割工具"
authors = [
    {name = "Developer", email = "<EMAIL>"}
]
readme = "README.md"
requires-python = ">=3.8"
classifiers = [
    "Development Status :: 3 - Alpha",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.8",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
]
dependencies = [
    "mcp>=1.0.0",
    "pandas>=2.0.0",
    "openpyxl>=3.1.0",
    "xlrd>=2.0.0",
    "sentence-transformers>=2.2.0",
    "numpy>=1.24.0",
    "scikit-learn>=1.3.0",
    "torch>=2.0.0",
    "transformers>=4.30.0",
    "pydantic>=2.0.0",
    "typing-extensions>=4.5.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.0.0",
    "pytest-asyncio>=0.21.0",
    "black>=23.0.0",
    "isort>=5.12.0",
    "mypy>=1.4.0",
    "flake8>=6.0.0",
]

[project.scripts]
mcp-semantic-splitter = "mcp_semantic_splitter.server:main"

[tool.setuptools.packages.find]
where = ["src"]

[tool.setuptools.package-dir]
"" = "src"

[tool.black]
line-length = 88
target-version = ['py38']

[tool.isort]
profile = "black"
line_length = 88

[tool.mypy]
python_version = "3.8"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
