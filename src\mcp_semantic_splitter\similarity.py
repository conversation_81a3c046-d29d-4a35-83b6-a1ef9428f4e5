"""
语义相似度计算模块
计算相邻片段间的余弦相似度，实现基于阈值的分割决策逻辑
"""

import numpy as np
from typing import List, Tuple, Optional
from sklearn.metrics.pairwise import cosine_similarity
import logging

logger = logging.getLogger(__name__)


class SimilarityCalculator:
    """语义相似度计算器"""
    
    def __init__(self, similarity_threshold: float = 0.7):
        """
        初始化相似度计算器
        
        Args:
            similarity_threshold: 相似度阈值，低于此值将进行分割
        """
        self.similarity_threshold = similarity_threshold
        self._validate_threshold()
    
    def _validate_threshold(self):
        """验证阈值参数"""
        if not 0.0 <= self.similarity_threshold <= 1.0:
            raise ValueError(f"相似度阈值必须在0.0到1.0之间，当前值: {self.similarity_threshold}")
    
    def calculate_pairwise_similarities(self, embeddings: np.ndarray) -> np.ndarray:
        """
        计算所有嵌入向量之间的相似度矩阵
        
        Args:
            embeddings: 嵌入向量数组，形状为 (n_chunks, embedding_dim)
            
        Returns:
            相似度矩阵，形状为 (n_chunks, n_chunks)
        """
        if embeddings.size == 0:
            return np.array([])
        
        if len(embeddings.shape) != 2:
            raise ValueError(f"嵌入向量必须是二维数组，当前形状: {embeddings.shape}")
        
        # 计算余弦相似度矩阵
        similarity_matrix = cosine_similarity(embeddings)
        
        logger.debug(f"计算了 {embeddings.shape[0]} 个向量的相似度矩阵")
        return similarity_matrix
    
    def calculate_adjacent_similarities(self, embeddings: np.ndarray) -> List[float]:
        """
        计算相邻片段之间的相似度
        
        Args:
            embeddings: 嵌入向量数组，形状为 (n_chunks, embedding_dim)
            
        Returns:
            相邻片段相似度列表，长度为 n_chunks-1
        """
        if embeddings.size == 0 or len(embeddings) < 2:
            return []
        
        similarities = []
        
        for i in range(len(embeddings) - 1):
            # 计算第i个和第i+1个片段的相似度
            sim = cosine_similarity([embeddings[i]], [embeddings[i + 1]])[0][0]
            similarities.append(float(sim))
        
        logger.debug(f"计算了 {len(similarities)} 个相邻片段的相似度")
        return similarities
    
    def find_split_points(self, similarities: List[float]) -> List[int]:
        """
        基于相似度阈值找到分割点
        
        Args:
            similarities: 相邻片段相似度列表
            
        Returns:
            分割点索引列表
        """
        split_points = []
        
        for i, sim in enumerate(similarities):
            if sim < self.similarity_threshold:
                # 在第i个和第i+1个片段之间分割
                split_points.append(i + 1)
        
        logger.debug(f"基于阈值 {self.similarity_threshold} 找到 {len(split_points)} 个分割点")
        return split_points
    
    def split_chunks_by_similarity(self, chunks: List[str], 
                                   embeddings: np.ndarray) -> List[List[str]]:
        """
        基于语义相似度分割文本片段
        
        Args:
            chunks: 原始文本片段列表
            embeddings: 对应的嵌入向量数组
            
        Returns:
            分割后的片段组列表
        """
        if len(chunks) != len(embeddings):
            raise ValueError("文本片段数量与嵌入向量数量不匹配")
        
        if len(chunks) <= 1:
            return [chunks] if chunks else []
        
        # 计算相邻相似度
        similarities = self.calculate_adjacent_similarities(embeddings)
        
        # 找到分割点
        split_points = self.find_split_points(similarities)
        
        # 执行分割
        groups = []
        start_idx = 0
        
        for split_idx in split_points:
            if split_idx > start_idx:
                groups.append(chunks[start_idx:split_idx])
            start_idx = split_idx
        
        # 添加最后一组
        if start_idx < len(chunks):
            groups.append(chunks[start_idx:])
        
        # 过滤空组
        groups = [group for group in groups if group]
        
        logger.info(f"将 {len(chunks)} 个片段分割为 {len(groups)} 组")
        return groups
    
    def analyze_similarity_distribution(self, similarities: List[float]) -> dict:
        """
        分析相似度分布统计信息
        
        Args:
            similarities: 相似度列表
            
        Returns:
            统计信息字典
        """
        if not similarities:
            return {
                "count": 0,
                "mean": 0.0,
                "std": 0.0,
                "min": 0.0,
                "max": 0.0,
                "below_threshold": 0,
                "above_threshold": 0
            }
        
        similarities_array = np.array(similarities)
        below_threshold = np.sum(similarities_array < self.similarity_threshold)
        above_threshold = len(similarities) - below_threshold
        
        return {
            "count": len(similarities),
            "mean": float(np.mean(similarities_array)),
            "std": float(np.std(similarities_array)),
            "min": float(np.min(similarities_array)),
            "max": float(np.max(similarities_array)),
            "below_threshold": int(below_threshold),
            "above_threshold": int(above_threshold),
            "threshold": self.similarity_threshold
        }
    
    def suggest_optimal_threshold(self, similarities: List[float], 
                                  target_groups: Optional[int] = None) -> float:
        """
        建议最优的相似度阈值
        
        Args:
            similarities: 相似度列表
            target_groups: 目标分组数量（可选）
            
        Returns:
            建议的阈值
        """
        if not similarities:
            return self.similarity_threshold
        
        similarities_sorted = sorted(similarities)
        
        if target_groups is not None and target_groups > 1:
            # 基于目标分组数量建议阈值
            if target_groups - 1 < len(similarities_sorted):
                suggested_threshold = similarities_sorted[target_groups - 1]
                return max(0.0, min(1.0, suggested_threshold))
        
        # 基于统计分布建议阈值
        mean_sim = np.mean(similarities)
        std_sim = np.std(similarities)
        
        # 使用均值减去一个标准差作为建议阈值
        suggested_threshold = mean_sim - std_sim
        
        # 确保阈值在合理范围内
        suggested_threshold = max(0.3, min(0.9, suggested_threshold))
        
        logger.info(f"建议的相似度阈值: {suggested_threshold:.3f}")
        return suggested_threshold
    
    def set_threshold(self, new_threshold: float):
        """设置新的相似度阈值"""
        self.similarity_threshold = new_threshold
        self._validate_threshold()
        logger.info(f"相似度阈值已更新为: {new_threshold}")
    
    def get_threshold(self) -> float:
        """获取当前相似度阈值"""
        return self.similarity_threshold
