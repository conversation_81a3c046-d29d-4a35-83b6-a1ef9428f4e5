"""
MCP服务器实现
提供语义分割功能的MCP服务器接口
"""

import asyncio
import json
import logging
from typing import Any, Dict, List, Optional
from pathlib import Path

from mcp.server import Server
from mcp.server.models import InitializationOptions
from mcp.server.stdio import stdio_server
from mcp.types import (
    CallToolRequest,
    CallToolResult,
    ListToolsRequest,
    ListToolsResult,
    Tool,
    TextContent,
    ImageContent,
    EmbeddedResource,
)
from pydantic import BaseModel, Field

from .splitter import SemanticSplitter

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class SplitFileRequest(BaseModel):
    """分割文件请求模型"""

    file_path: str = Field(description="文件路径")
    sheet_name: Optional[str] = Field(
        default=None, description="工作表名称（仅Excel文件）"
    )
    similarity_threshold: Optional[float] = Field(
        default=None, description="相似度阈值"
    )
    min_chunk_size: Optional[int] = Field(default=None, description="最小片段大小")
    max_chunk_size: Optional[int] = Field(default=None, description="最大片段大小")


class SplitTextRequest(BaseModel):
    """分割文本请求模型"""

    chunks: List[str] = Field(description="文本片段列表")
    similarity_threshold: Optional[float] = Field(
        default=None, description="相似度阈值"
    )
    min_chunk_size: Optional[int] = Field(default=None, description="最小片段大小")
    max_chunk_size: Optional[int] = Field(default=None, description="最大片段大小")


class ConfigRequest(BaseModel):
    """配置请求模型"""

    similarity_threshold: Optional[float] = Field(
        default=None, description="相似度阈值"
    )
    min_chunk_size: Optional[int] = Field(default=None, description="最小片段大小")
    max_chunk_size: Optional[int] = Field(default=None, description="最大片段大小")
    max_merge_distance: Optional[int] = Field(default=None, description="最大合并距离")
    model_name: Optional[str] = Field(default=None, description="嵌入模型名称")
    device: Optional[str] = Field(default=None, description="计算设备")


class MCPSemanticSplitterServer:
    """MCP语义分割服务器"""

    def __init__(self):
        self.server = Server("semantic-splitter")
        self.splitter = None
        self._setup_handlers()

    def _setup_handlers(self):
        """设置处理器"""

        @self.server.list_tools()
        async def handle_list_tools() -> ListToolsResult:
            """列出可用工具"""
            return ListToolsResult(
                tools=[
                    Tool(
                        name="split_file",
                        description="分割表格文件（支持Excel、CSV等格式）",
                        inputSchema={
                            "type": "object",
                            "properties": {
                                "file_path": {
                                    "type": "string",
                                    "description": "文件路径",
                                },
                                "sheet_name": {
                                    "type": "string",
                                    "description": "工作表名称（仅Excel文件）",
                                },
                                "similarity_threshold": {
                                    "type": "number",
                                    "description": "相似度阈值 (0.0-1.0)",
                                    "minimum": 0.0,
                                    "maximum": 1.0,
                                },
                                "min_chunk_size": {
                                    "type": "integer",
                                    "description": "最小片段大小（字符数）",
                                    "minimum": 1,
                                },
                                "max_chunk_size": {
                                    "type": "integer",
                                    "description": "最大片段大小（字符数）",
                                    "minimum": 1,
                                },
                            },
                            "required": ["file_path"],
                        },
                    ),
                    Tool(
                        name="split_text",
                        description="分割文本片段列表",
                        inputSchema={
                            "type": "object",
                            "properties": {
                                "chunks": {
                                    "type": "array",
                                    "items": {"type": "string"},
                                    "description": "文本片段列表",
                                },
                                "similarity_threshold": {
                                    "type": "number",
                                    "description": "相似度阈值 (0.0-1.0)",
                                    "minimum": 0.0,
                                    "maximum": 1.0,
                                },
                                "min_chunk_size": {
                                    "type": "integer",
                                    "description": "最小片段大小（字符数）",
                                    "minimum": 1,
                                },
                                "max_chunk_size": {
                                    "type": "integer",
                                    "description": "最大片段大小（字符数）",
                                    "minimum": 1,
                                },
                            },
                            "required": ["chunks"],
                        },
                    ),
                    Tool(
                        name="get_supported_formats",
                        description="获取支持的文件格式列表",
                        inputSchema={"type": "object", "properties": {}},
                    ),
                    Tool(
                        name="get_sheet_names",
                        description="获取Excel文件的工作表名称",
                        inputSchema={
                            "type": "object",
                            "properties": {
                                "file_path": {
                                    "type": "string",
                                    "description": "Excel文件路径",
                                }
                            },
                            "required": ["file_path"],
                        },
                    ),
                    Tool(
                        name="get_config",
                        description="获取当前配置",
                        inputSchema={"type": "object", "properties": {}},
                    ),
                    Tool(
                        name="update_config",
                        description="更新配置参数",
                        inputSchema={
                            "type": "object",
                            "properties": {
                                "similarity_threshold": {
                                    "type": "number",
                                    "description": "相似度阈值 (0.0-1.0)",
                                    "minimum": 0.0,
                                    "maximum": 1.0,
                                },
                                "min_chunk_size": {
                                    "type": "integer",
                                    "description": "最小片段大小（字符数）",
                                    "minimum": 1,
                                },
                                "max_chunk_size": {
                                    "type": "integer",
                                    "description": "最大片段大小（字符数）",
                                    "minimum": 1,
                                },
                                "max_merge_distance": {
                                    "type": "integer",
                                    "description": "最大合并距离",
                                    "minimum": 1,
                                },
                            },
                        },
                    ),
                    Tool(
                        name="get_model_info",
                        description="获取嵌入模型信息",
                        inputSchema={"type": "object", "properties": {}},
                    ),
                ]
            )

        @self.server.call_tool()
        async def handle_call_tool(request: CallToolRequest) -> CallToolResult:
            """处理工具调用"""
            try:
                # 确保分割器已初始化
                if self.splitter is None:
                    self.splitter = SemanticSplitter()

                if request.name == "split_file":
                    return await self._handle_split_file(request.arguments)
                elif request.name == "split_text":
                    return await self._handle_split_text(request.arguments)
                elif request.name == "get_supported_formats":
                    return await self._handle_get_supported_formats()
                elif request.name == "get_sheet_names":
                    return await self._handle_get_sheet_names(request.arguments)
                elif request.name == "get_config":
                    return await self._handle_get_config()
                elif request.name == "update_config":
                    return await self._handle_update_config(request.arguments)
                elif request.name == "get_model_info":
                    return await self._handle_get_model_info()
                else:
                    raise ValueError(f"未知工具: {request.name}")

            except Exception as e:
                logger.error(f"工具调用失败: {str(e)}")
                return CallToolResult(
                    content=[TextContent(type="text", text=f"错误: {str(e)}")],
                    isError=True,
                )

    async def _handle_split_file(self, arguments: Dict[str, Any]) -> CallToolResult:
        """处理文件分割请求"""
        request = SplitFileRequest(**arguments)

        # 更新临时配置
        if any(
            getattr(request, attr) is not None
            for attr in ["similarity_threshold", "min_chunk_size", "max_chunk_size"]
        ):
            temp_config = {
                k: v
                for k, v in {
                    "similarity_threshold": request.similarity_threshold,
                    "min_chunk_size": request.min_chunk_size,
                    "max_chunk_size": request.max_chunk_size,
                }.items()
                if v is not None
            }
            self.splitter.update_config(**temp_config)

        result = self.splitter.split_file(request.file_path, request.sheet_name)

        return CallToolResult(
            content=[
                TextContent(
                    type="text", text=json.dumps(result, ensure_ascii=False, indent=2)
                )
            ]
        )

    async def _handle_split_text(self, arguments: Dict[str, Any]) -> CallToolResult:
        """处理文本分割请求"""
        request = SplitTextRequest(**arguments)

        # 更新临时配置
        if any(
            getattr(request, attr) is not None
            for attr in ["similarity_threshold", "min_chunk_size", "max_chunk_size"]
        ):
            temp_config = {
                k: v
                for k, v in {
                    "similarity_threshold": request.similarity_threshold,
                    "min_chunk_size": request.min_chunk_size,
                    "max_chunk_size": request.max_chunk_size,
                }.items()
                if v is not None
            }
            self.splitter.update_config(**temp_config)

        result = self.splitter.split_text_chunks(request.chunks)

        return CallToolResult(
            content=[
                TextContent(
                    type="text", text=json.dumps(result, ensure_ascii=False, indent=2)
                )
            ]
        )

    async def _handle_get_supported_formats(self) -> CallToolResult:
        """处理获取支持格式请求"""
        formats = self.splitter.get_supported_formats()
        result = {"supported_formats": formats, "description": "支持的表格文件格式"}

        return CallToolResult(
            content=[
                TextContent(
                    type="text", text=json.dumps(result, ensure_ascii=False, indent=2)
                )
            ]
        )

    async def _handle_get_sheet_names(
        self, arguments: Dict[str, Any]
    ) -> CallToolResult:
        """处理获取工作表名称请求"""
        file_path = arguments.get("file_path")
        if not file_path:
            raise ValueError("缺少file_path参数")

        sheet_names = self.splitter.get_sheet_names(file_path)
        result = {"file_path": file_path, "sheet_names": sheet_names}

        return CallToolResult(
            content=[
                TextContent(
                    type="text", text=json.dumps(result, ensure_ascii=False, indent=2)
                )
            ]
        )

    async def _handle_get_config(self) -> CallToolResult:
        """处理获取配置请求"""
        config = self.splitter.get_config()

        return CallToolResult(
            content=[
                TextContent(
                    type="text", text=json.dumps(config, ensure_ascii=False, indent=2)
                )
            ]
        )

    async def _handle_update_config(self, arguments: Dict[str, Any]) -> CallToolResult:
        """处理更新配置请求"""
        # 过滤掉None值
        config_updates = {k: v for k, v in arguments.items() if v is not None}

        if config_updates:
            self.splitter.update_config(**config_updates)

        new_config = self.splitter.get_config()
        result = {
            "message": "配置已更新",
            "updated_fields": list(config_updates.keys()),
            "current_config": new_config,
        }

        return CallToolResult(
            content=[
                TextContent(
                    type="text", text=json.dumps(result, ensure_ascii=False, indent=2)
                )
            ]
        )

    async def _handle_get_model_info(self) -> CallToolResult:
        """处理获取模型信息请求"""
        model_info = self.splitter.get_model_info()

        return CallToolResult(
            content=[
                TextContent(
                    type="text",
                    text=json.dumps(model_info, ensure_ascii=False, indent=2),
                )
            ]
        )

    async def run(self):
        """运行服务器"""
        async with stdio_server() as (read_stream, write_stream):
            await self.server.run(
                read_stream,
                write_stream,
                InitializationOptions(
                    server_name="semantic-splitter",
                    server_version="0.1.0",
                    capabilities=self.server.get_capabilities(
                        notification_options=None, experimental_capabilities={}
                    ),
                ),
            )


async def main():
    """主函数"""
    server = MCPSemanticSplitterServer()
    await server.run()


if __name__ == "__main__":
    asyncio.run(main())
