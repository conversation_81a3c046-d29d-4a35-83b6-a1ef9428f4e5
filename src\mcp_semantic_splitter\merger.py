"""
智能合并模块
对过小的片段进行智能合并，确保每个片段都有足够的信息量且语义完整
"""

import numpy as np
from typing import List, Tuple, Optional
from sklearn.metrics.pairwise import cosine_similarity
import logging

logger = logging.getLogger(__name__)


class ChunkMerger:
    """文本片段智能合并器"""
    
    def __init__(self, 
                 min_chunk_size: int = 50,
                 max_chunk_size: int = 1000,
                 max_merge_distance: int = 2):
        """
        初始化合并器
        
        Args:
            min_chunk_size: 最小片段大小（字符数）
            max_chunk_size: 最大片段大小（字符数）
            max_merge_distance: 最大合并距离（片段间隔数）
        """
        self.min_chunk_size = min_chunk_size
        self.max_chunk_size = max_chunk_size
        self.max_merge_distance = max_merge_distance
        self._validate_parameters()
    
    def _validate_parameters(self):
        """验证参数"""
        if self.min_chunk_size <= 0:
            raise ValueError(f"最小片段大小必须大于0，当前值: {self.min_chunk_size}")
        
        if self.max_chunk_size <= self.min_chunk_size:
            raise ValueError(f"最大片段大小必须大于最小片段大小，当前值: {self.max_chunk_size}")
        
        if self.max_merge_distance <= 0:
            raise ValueError(f"最大合并距离必须大于0，当前值: {self.max_merge_distance}")
    
    def merge_small_chunks(self, 
                          chunk_groups: List[List[str]], 
                          embeddings_groups: Optional[List[np.ndarray]] = None) -> List[List[str]]:
        """
        合并过小的片段组
        
        Args:
            chunk_groups: 分割后的片段组列表
            embeddings_groups: 对应的嵌入向量组列表（可选）
            
        Returns:
            合并后的片段组列表
        """
        if not chunk_groups:
            return []
        
        merged_groups = []
        i = 0
        
        while i < len(chunk_groups):
            current_group = chunk_groups[i]
            current_text = self._join_chunks(current_group)
            
            # 如果当前组足够大，直接添加
            if len(current_text) >= self.min_chunk_size:
                merged_groups.append(current_group)
                i += 1
                continue
            
            # 当前组过小，尝试合并
            best_merge_target = self._find_best_merge_target(
                i, chunk_groups, embeddings_groups
            )
            
            if best_merge_target is not None:
                # 执行合并
                target_idx = best_merge_target
                merged_group = self._merge_groups(
                    chunk_groups[i], chunk_groups[target_idx]
                )
                
                # 检查合并后是否超过最大大小
                merged_text = self._join_chunks(merged_group)
                if len(merged_text) <= self.max_chunk_size:
                    # 合并成功
                    if target_idx == i + 1:
                        # 与下一个组合并
                        merged_groups.append(merged_group)
                        i += 2  # 跳过下一个组
                    else:
                        # 与前一个组合并
                        merged_groups[-1] = merged_group
                        i += 1
                else:
                    # 合并后过大，保持原样
                    merged_groups.append(current_group)
                    i += 1
            else:
                # 无法合并，保持原样
                merged_groups.append(current_group)
                i += 1
        
        logger.info(f"合并前: {len(chunk_groups)} 组，合并后: {len(merged_groups)} 组")
        return merged_groups
    
    def _find_best_merge_target(self, 
                               current_idx: int,
                               chunk_groups: List[List[str]],
                               embeddings_groups: Optional[List[np.ndarray]] = None) -> Optional[int]:
        """
        找到最佳的合并目标
        
        Args:
            current_idx: 当前组索引
            chunk_groups: 所有片段组
            embeddings_groups: 对应的嵌入向量组
            
        Returns:
            最佳合并目标的索引，如果没有则返回None
        """
        candidates = []
        
        # 检查前面的组
        for i in range(max(0, current_idx - self.max_merge_distance), current_idx):
            if self._can_merge(chunk_groups[current_idx], chunk_groups[i]):
                similarity = self._calculate_group_similarity(
                    current_idx, i, embeddings_groups
                )
                candidates.append((i, similarity))
        
        # 检查后面的组
        for i in range(current_idx + 1, 
                      min(len(chunk_groups), current_idx + self.max_merge_distance + 1)):
            if self._can_merge(chunk_groups[current_idx], chunk_groups[i]):
                similarity = self._calculate_group_similarity(
                    current_idx, i, embeddings_groups
                )
                candidates.append((i, similarity))
        
        if not candidates:
            return None
        
        # 选择相似度最高的候选者
        best_candidate = max(candidates, key=lambda x: x[1])
        return best_candidate[0]
    
    def _can_merge(self, group1: List[str], group2: List[str]) -> bool:
        """
        检查两个组是否可以合并
        
        Args:
            group1: 第一个组
            group2: 第二个组
            
        Returns:
            是否可以合并
        """
        merged_text = self._join_chunks(group1 + group2)
        return len(merged_text) <= self.max_chunk_size
    
    def _calculate_group_similarity(self, 
                                   idx1: int, 
                                   idx2: int,
                                   embeddings_groups: Optional[List[np.ndarray]] = None) -> float:
        """
        计算两个组之间的相似度
        
        Args:
            idx1: 第一个组的索引
            idx2: 第二个组的索引
            embeddings_groups: 嵌入向量组列表
            
        Returns:
            相似度分数
        """
        if embeddings_groups is None or idx1 >= len(embeddings_groups) or idx2 >= len(embeddings_groups):
            # 如果没有嵌入向量，使用距离作为相似度（距离越近相似度越高）
            distance = abs(idx1 - idx2)
            return 1.0 / (1.0 + distance)
        
        emb1 = embeddings_groups[idx1]
        emb2 = embeddings_groups[idx2]
        
        if emb1.size == 0 or emb2.size == 0:
            return 0.0
        
        # 计算组内平均嵌入向量
        avg_emb1 = np.mean(emb1, axis=0) if len(emb1.shape) > 1 else emb1
        avg_emb2 = np.mean(emb2, axis=0) if len(emb2.shape) > 1 else emb2
        
        # 计算余弦相似度
        similarity = cosine_similarity([avg_emb1], [avg_emb2])[0][0]
        return float(similarity)
    
    def _merge_groups(self, group1: List[str], group2: List[str]) -> List[str]:
        """
        合并两个组
        
        Args:
            group1: 第一个组
            group2: 第二个组
            
        Returns:
            合并后的组
        """
        return group1 + group2
    
    def _join_chunks(self, chunks: List[str]) -> str:
        """
        将片段列表连接为单个文本
        
        Args:
            chunks: 文本片段列表
            
        Returns:
            连接后的文本
        """
        return " ".join(chunks)
    
    def merge_within_groups(self, chunk_groups: List[List[str]]) -> List[str]:
        """
        将每个组内的片段合并为单个文本
        
        Args:
            chunk_groups: 片段组列表
            
        Returns:
            合并后的文本列表
        """
        merged_texts = []
        
        for group in chunk_groups:
            if group:
                merged_text = self._join_chunks(group)
                merged_texts.append(merged_text)
        
        logger.info(f"将 {len(chunk_groups)} 个组合并为 {len(merged_texts)} 个文本片段")
        return merged_texts
    
    def analyze_chunk_sizes(self, chunks: List[str]) -> dict:
        """
        分析片段大小分布
        
        Args:
            chunks: 文本片段列表
            
        Returns:
            大小分布统计信息
        """
        if not chunks:
            return {
                "count": 0,
                "total_chars": 0,
                "mean_size": 0.0,
                "min_size": 0,
                "max_size": 0,
                "small_chunks": 0,
                "large_chunks": 0
            }
        
        sizes = [len(chunk) for chunk in chunks]
        small_chunks = sum(1 for size in sizes if size < self.min_chunk_size)
        large_chunks = sum(1 for size in sizes if size > self.max_chunk_size)
        
        return {
            "count": len(chunks),
            "total_chars": sum(sizes),
            "mean_size": np.mean(sizes),
            "min_size": min(sizes),
            "max_size": max(sizes),
            "small_chunks": small_chunks,
            "large_chunks": large_chunks,
            "min_threshold": self.min_chunk_size,
            "max_threshold": self.max_chunk_size
        }
    
    def set_parameters(self, 
                      min_chunk_size: Optional[int] = None,
                      max_chunk_size: Optional[int] = None,
                      max_merge_distance: Optional[int] = None):
        """更新合并参数"""
        if min_chunk_size is not None:
            self.min_chunk_size = min_chunk_size
        if max_chunk_size is not None:
            self.max_chunk_size = max_chunk_size
        if max_merge_distance is not None:
            self.max_merge_distance = max_merge_distance
        
        self._validate_parameters()
        logger.info(f"合并参数已更新: min={self.min_chunk_size}, max={self.max_chunk_size}, distance={self.max_merge_distance}")
