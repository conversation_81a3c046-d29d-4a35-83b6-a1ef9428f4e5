Metadata-Version: 2.4
Name: mcp-semantic-splitter
Version: 0.1.0
Summary: MCP服务器：基于语义相似度的表格文档智能分割工具
Author-email: Developer <<EMAIL>>
Classifier: Development Status :: 3 - Alpha
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: MIT License
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Requires-Python: >=3.8
Description-Content-Type: text/markdown
Requires-Dist: mcp>=1.0.0
Requires-Dist: pandas>=2.0.0
Requires-Dist: openpyxl>=3.1.0
Requires-Dist: xlrd>=2.0.0
Requires-Dist: sentence-transformers>=2.2.0
Requires-Dist: numpy>=1.24.0
Requires-Dist: scikit-learn>=1.3.0
Requires-Dist: torch>=2.0.0
Requires-Dist: transformers>=4.30.0
Requires-Dist: pydantic>=2.0.0
Requires-Dist: typing-extensions>=4.5.0
Provides-Extra: dev
Requires-Dist: pytest>=7.0.0; extra == "dev"
Requires-Dist: pytest-asyncio>=0.21.0; extra == "dev"
Requires-Dist: black>=23.0.0; extra == "dev"
Requires-Dist: isort>=5.12.0; extra == "dev"
Requires-Dist: mypy>=1.4.0; extra == "dev"
Requires-Dist: flake8>=6.0.0; extra == "dev"

# MCP语义分割器

基于语义相似度分析的长文档智能切分工具，专门用于处理表格文档（Excel、CSV等）。

## 功能特性

- 支持多种表格格式：xlsx, xls, csv, xlsb等
- 基于语义相似度的智能分割
- 使用预训练的向量嵌入模型
- 智能合并过小片段
- MCP服务器接口

## 安装

```bash
pip install -e .
```

## 使用方法

### 作为MCP服务器运行

```bash
mcp-semantic-splitter
```

### 配置参数

- `similarity_threshold`: 语义相似度阈值 (默认: 0.7)
- `min_chunk_size`: 最小片段大小 (默认: 50字符)
- `max_chunk_size`: 最大片段大小 (默认: 1000字符)
- `model_name`: 嵌入模型名称 (默认: "sentence-transformers/paraphrase-multilingual-MiniLM-L12-v2")

## 支持的文件格式

- Excel文件: .xlsx, .xls, .xlsb, .xlsm
- CSV文件: .csv
- 其他表格格式: .et, .ett, .ets

## 算法原理

1. **初步切分**: 使用基础分隔符对文档进行初步切分
2. **向量嵌入**: 将文本片段转换为语义向量
3. **相似度计算**: 计算相邻片段的余弦相似度
4. **分割决策**: 基于阈值进行分割
5. **智能合并**: 合并过小的片段

## 开发

```bash
# 安装开发依赖
pip install -e ".[dev]"

# 运行测试
pytest

# 代码格式化
black src/
isort src/
```
