# MCP语义分割器

基于语义相似度分析的长文档智能切分工具，专门用于处理表格文档（Excel、CSV等）。

## 功能特性

- 支持多种表格格式：xlsx, xls, csv, xlsb等
- 基于语义相似度的智能分割
- 使用预训练的向量嵌入模型
- 智能合并过小片段
- MCP服务器接口

## 安装

```bash
pip install -e .
```

## 使用方法

### 作为MCP服务器运行

```bash
mcp-semantic-splitter
```

### 配置参数

- `similarity_threshold`: 语义相似度阈值 (默认: 0.7)
- `min_chunk_size`: 最小片段大小 (默认: 50字符)
- `max_chunk_size`: 最大片段大小 (默认: 1000字符)
- `model_name`: 嵌入模型名称 (默认: "sentence-transformers/paraphrase-multilingual-MiniLM-L12-v2")

## 支持的文件格式

- Excel文件: .xlsx, .xls, .xlsb, .xlsm
- CSV文件: .csv
- 其他表格格式: .et, .ett, .ets

## 算法原理

1. **初步切分**: 使用基础分隔符对文档进行初步切分
2. **向量嵌入**: 将文本片段转换为语义向量
3. **相似度计算**: 计算相邻片段的余弦相似度
4. **分割决策**: 基于阈值进行分割
5. **智能合并**: 合并过小的片段

## 开发

```bash
# 安装开发依赖
pip install -e ".[dev]"

# 运行测试
pytest

# 代码格式化
black src/
isort src/
```
