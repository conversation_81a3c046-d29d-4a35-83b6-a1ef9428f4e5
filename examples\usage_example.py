"""
MCP语义分割器使用示例
"""

import asyncio
import json
from pathlib import Path
import sys
import os

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from mcp_semantic_splitter import SemanticSplitter


async def main():
    """主函数"""
    print("=== MCP语义分割器使用示例 ===\n")
    
    # 1. 初始化分割器
    print("1. 初始化语义分割器...")
    splitter = SemanticSplitter(
        similarity_threshold=0.7,
        min_chunk_size=30,
        max_chunk_size=200,
        model_name="sentence-transformers/all-MiniLM-L6-v2"  # 使用较小的模型
    )
    print("✓ 分割器初始化完成\n")
    
    # 2. 获取支持的文件格式
    print("2. 支持的文件格式:")
    formats = splitter.get_supported_formats()
    print(f"   {', '.join(formats)}\n")
    
    # 3. 分割示例CSV文件
    print("3. 分割示例CSV文件...")
    csv_file = Path(__file__).parent / "sample_data.csv"
    
    if csv_file.exists():
        try:
            result = splitter.split_file(str(csv_file))
            
            if result["success"]:
                print(f"✓ 文件分割成功!")
                print(f"   原始片段数: {result['original_chunks_count']}")
                print(f"   最终片段数: {result['final_chunks_count']}")
                print(f"   压缩比: {result['statistics']['compression_ratio']:.2f}")
                
                print("\n   分割结果:")
                for i, chunk in enumerate(result["chunks"][:3], 1):  # 只显示前3个
                    print(f"   片段{i}: {chunk[:100]}...")
                
                if len(result["chunks"]) > 3:
                    print(f"   ... 还有 {len(result['chunks']) - 3} 个片段")
                    
            else:
                print(f"✗ 文件分割失败: {result.get('error', '未知错误')}")
                
        except Exception as e:
            print(f"✗ 分割过程出错: {str(e)}")
    else:
        print(f"✗ 示例文件不存在: {csv_file}")
    
    print()
    
    # 4. 分割文本片段示例
    print("4. 分割文本片段示例...")
    text_chunks = [
        "苹果是一种常见的水果，富含维生素C",
        "香蕉含有丰富的钾元素，对心脏健康有益",
        "橙子也是维生素C的良好来源",
        "汽车是现代社会重要的交通工具",
        "飞机让长距离旅行变得更加便捷",
        "火车是环保的交通方式之一",
        "电脑已经成为工作和学习的必需品",
        "手机改变了人们的沟通方式",
        "互联网连接了全世界"
    ]
    
    try:
        result = splitter.split_text_chunks(text_chunks)
        
        if result["success"]:
            print(f"✓ 文本分割成功!")
            print(f"   原始片段数: {result['original_chunks_count']}")
            print(f"   最终片段数: {result['final_chunks_count']}")
            
            print("\n   分割结果:")
            for i, chunk in enumerate(result["chunks"], 1):
                print(f"   组{i}: {chunk}")
                
        else:
            print(f"✗ 文本分割失败: {result.get('error', '未知错误')}")
            
    except Exception as e:
        print(f"✗ 分割过程出错: {str(e)}")
    
    print()
    
    # 5. 显示配置信息
    print("5. 当前配置:")
    config = splitter.get_config()
    for key, value in config.items():
        print(f"   {key}: {value}")
    
    print()
    
    # 6. 显示模型信息
    print("6. 模型信息:")
    try:
        model_info = splitter.get_model_info()
        for key, value in model_info.items():
            print(f"   {key}: {value}")
    except Exception as e:
        print(f"   获取模型信息失败: {str(e)}")
    
    print("\n=== 示例完成 ===")


if __name__ == "__main__":
    asyncio.run(main())
